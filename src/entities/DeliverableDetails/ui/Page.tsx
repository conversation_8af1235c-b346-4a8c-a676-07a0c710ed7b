import {
  <PERSON>Down,
  ChevronLeft,
  <PERSON><PERSON>,
  Eye,
  EyeSlash,
  PencilSquare,
  Trash,
} from 'react-bootstrap-icons';
import {
  Button,
  Container,
  Skeleton,
  Typography,
} from '@ghq-abi/design-system-v2';
import { useTranslate } from '@tolgee/react';

import { ContentPage } from '~/app/templates/ContentPage';
import { ActionModal } from '~/shared/components/ActionModal';
import { GlobalAdminProtectedFragment } from '~/shared/components/GlobalAdminProtectedFragment';
import { DeliverableTypeEnum } from '~/shared/types/Deliverable';

import { CardItem } from '../components/CardItem';
import { useCatalogItemsDetails } from '../hooks';

export function Page({ deliverableUid }: { deliverableUid: string }) {
  const { t } = useTranslate();

  const {
    data,
    isLoading,
    transformedData,
    handleNavigateBack,
    handleDownloadSnapshot,
    handleCopyLink,
    handledeleteDeliverable,
    isLoadingDelete,
    isLoadingToggle,
    handleNavigateToEdit,
    gridRef,
    contentRef,
    actionModal,
    successModal,
    handleupdateDeliverableStatus,
  } = useCatalogItemsDetails(deliverableUid);

  return (
    <>
      <ContentPage
        css={{
          '@lg': { pt: 0, px: '$md' },
        }}
        contentCss={{ bg: '$gray100', p: 0, gap: '$5' }}
        transparentContent
      >
        <ActionModal
          isOpen={actionModal.isOpen}
          openModal={actionModal.openModal}
          closeModal={actionModal.closeModal}
          title={actionModal.title}
          message={actionModal.message}
          actions={[
            {
              label: t('common_yes'),
              onClick: actionModal.handleConfirm,
              isLoading: isLoadingDelete || isLoadingToggle,
              variant: 'primary',
            },
            {
              label: t('common_no'),
              onClick: actionModal.closeModal,
              variant: 'secondary',
            },
          ]}
        />
        <ActionModal
          isOpen={successModal.isOpen}
          openModal={successModal.openModal}
          closeModal={successModal.handleUnderstood}
          title={successModal.title}
          message={successModal.message}
          variant={successModal.variant}
          actions={[
            {
              label: t('common_understood'),
              onClick: successModal.handleUnderstood,
              variant: 'primary',
            },
          ]}
        />
        {data && !isLoading ? (
          <Container className="flex flex-col gap-4">
            <Container className="flex justify-between">
              <Container ref={contentRef}>
                <Button
                  iconLeft={<ChevronLeft />}
                  variant="tertiary"
                  onClick={handleNavigateBack}
                  className="p-0"
                >
                  <Typography variant="title-md-bold" color="dark">
                    {data?.name}
                  </Typography>
                </Button>
              </Container>
              <Container className="flex gap-2 justify-end">
                <GlobalAdminProtectedFragment>
                  <Button
                    variant="secondary"
                    border="default"
                    iconRight={<Trash />}
                    onClick={handledeleteDeliverable}
                    isLoading={isLoadingDelete}
                  >
                    {t('common_delete')}
                  </Button>
                  <Button
                    variant="secondary"
                    border="default"
                    iconRight={data.isActive ? <EyeSlash /> : <Eye />}
                    onClick={() =>
                      handleupdateDeliverableStatus(
                        !data.isActive ? 'ACTIVE' : 'INACTIVE',
                      )
                    }
                  >
                    {!data.isActive
                      ? t('common_status_active')
                      : t('common_status_inactive')}
                  </Button>
                  <Button
                    variant="secondary"
                    border="default"
                    iconRight={<PencilSquare />}
                    onClick={handleNavigateToEdit}
                  >
                    {t('common_edit')}
                  </Button>
                </GlobalAdminProtectedFragment>
                <Button
                  variant="secondary"
                  border="default"
                  iconRight={<ArrowDown />}
                  onClick={handleDownloadSnapshot}
                >
                  {t('common_download')}
                </Button>
                <Button
                  variant="primary"
                  border="none"
                  iconRight={<Copy />}
                  onClick={handleCopyLink}
                >
                  {t('common_copy_link')}
                </Button>
              </Container>
            </Container>
            <Container ref={gridRef} className="grid grid-cols-4 gap-6">
              {transformedData.items.map((item, index) => (
                <CardItem
                  key={index}
                  item={item as any}
                  isActive={transformedData?.isActive}
                  deliverableType={
                    transformedData?.deliverableType as DeliverableTypeEnum
                  }
                  deliverableCount={transformedData?.deliverableCount}
                />
              ))}
            </Container>
          </Container>
        ) : (
          <Container className="flex flex-col gap-4">
            <Container className="flex justify-between">
              <Container>
                <Skeleton className="h-[40px] w-[120px] bg-gray-200" />
              </Container>
              <Container className="flex gap-2 justify-end">
                <GlobalAdminProtectedFragment>
                  <Skeleton className="h-[40px] w-[120px] bg-gray-200" />
                </GlobalAdminProtectedFragment>
                <GlobalAdminProtectedFragment>
                  <Skeleton className="h-[40px] w-[120px] bg-gray-200" />
                </GlobalAdminProtectedFragment>
                <GlobalAdminProtectedFragment>
                  <Skeleton className="h-[40px] w-[120px] bg-gray-200" />
                </GlobalAdminProtectedFragment>
                <Skeleton className="h-[40px] w-[120px] bg-gray-200" />
                <Skeleton className="h-[40px] w-[215px] bg-gray-200" />
              </Container>
            </Container>
            <Container ref={gridRef} className="grid grid-cols-4 gap-6">
              {Array.from({ length: 4 }).map((_, index) => (
                <Skeleton
                  key={index}
                  className="h-[73px] w-[418px] bg-gray-200"
                />
              ))}
              {Array.from({ length: 4 }).map((_, index) => (
                <Skeleton
                  key={index}
                  className="h-[210px] w-[418px] bg-gray-200"
                />
              ))}
              {Array.from({ length: 2 }).map((_, index) => (
                <Skeleton
                  key={index}
                  className="h-[331px] col-span-2 bg-gray-200"
                />
              ))}
            </Container>
          </Container>
        )}
      </ContentPage>
    </>
  );
}
