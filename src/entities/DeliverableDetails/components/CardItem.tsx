import React from 'react';
import { useTranslate } from '@tolgee/react';

import { useToast } from '~/app/contexts/ToastContext';
import {
  CatalogItemDetailCardDeliverables,
  CatalogItemDetailCardGlobalOwners,
  CatalogItemDetailCardSimple,
  CatalogItemDetailCardText,
} from '~/shared/components/CatalogItemDetailCard';
import { PACalculationComponent } from '~/shared/components/PACalculationComponent';
import { DeliverableTypeEnum } from '~/shared/types/Deliverable';
import { Owners } from '~/shared/types/Owners';
import { CatalogItemDetailsCardContentTypesEnum } from '~/shared/utils/enums';

import type { CatalogItemCardData } from '../types';

type CardItemProps = {
  item: CatalogItemCardData;
  onCopy?: () => void;
  isActive?: boolean;
  deliverableType?: DeliverableTypeEnum;
  deliverableCount?: number;
};

export function CardItem({
  item,
  onCopy,
  isActive,
  deliverableType,
  deliverableCount = 0,
}: CardItemProps) {
  const { info, content, cardType, icon } = item;
  const toast = useToast();
  const { t } = useTranslate();

  const handleCopy = () => {
    if (onCopy) {
      onCopy();
      return;
    }

    switch (cardType) {
      case CatalogItemDetailsCardContentTypesEnum.BULLET_LIST:
        navigator.clipboard.writeText(
          content && Array.isArray(content)
            ? (content as string[]).join(', ')
            : (content as string),
        );
        break;
      case CatalogItemDetailsCardContentTypesEnum.USER_LIST:
        navigator.clipboard.writeText(
          (content as Owners[]).map(user => user.name).join(', '),
        );
        break;
      default:
        navigator.clipboard.writeText(content as string);
    }

    toast.add({
      title: t('common_copied_to_clipboard'),
      description: t('common_text_copied_to_clipboard'),
      type: 'success',
    });
  };

  const cardMap: Record<string, React.ReactElement> = {
    TYPE: (
      <CatalogItemDetailCardSimple
        info={t('common_type')}
        content={content}
        onCopy={handleCopy}
        icon={icon}
        className="h-[73px]"
        isActive={isActive}
        type={deliverableType}
      />
    ),
    FUNCTION: (
      <CatalogItemDetailCardSimple
        info={t('common_function')}
        content={content}
        onCopy={handleCopy}
        icon={icon}
        className="h-[73px]"
        type={deliverableType}
      />
    ),
    FREQUENCY: (
      <CatalogItemDetailCardSimple
        info={t('common_frequency')}
        content={content}
        onCopy={handleCopy}
        icon={icon}
        className="h-[73px]"
        type={deliverableType}
      />
    ),
    USAGE: (
      <CatalogItemDetailCardSimple
        info={t('common_usage')}
        content={content}
        onCopy={handleCopy}
        icon={icon}
        className="h-[73px]"
        type={deliverableType}
      />
    ),
    DEFINITION: (
      <CatalogItemDetailCardText
        info={t('common_definition')}
        onCopy={handleCopy}
        content={content}
        icon={icon}
        className="h-[210px]"
        type={deliverableType}
      />
    ),
    'DATA SOURCE': (
      <CatalogItemDetailCardText
        info={t('common_data_source')}
        onCopy={handleCopy}
        content={content}
        icon={icon}
        className="h-[210px]"
        type={deliverableType}
      />
    ),
    'BUSINESS LEVEL AGGREGATION': (
      <CatalogItemDetailCardText
        info={t('common_bu_level_aggregation')}
        onCopy={handleCopy}
        content={content}
        icon={icon}
        className="h-[210px]"
        type={deliverableType}
      />
    ),
    'GLOBAL OWNER': (
      <CatalogItemDetailCardGlobalOwners
        info={t('common_global_owner')}
        content={content}
        onCopy={handleCopy}
        icon={icon}
        className="h-[210px] overflow-hidden"
        type={deliverableType}
      />
    ),
    'CALCULATION METHOD': (
      <CatalogItemDetailCardText
        info={t('common_calculation_method')}
        content={content}
        onCopy={handleCopy}
        icon={icon}
        className="col-span-2 h-[331px]"
        type={deliverableType}
      />
    ),
    'PA VALUE':
      deliverableType === DeliverableTypeEnum.PROJECT ? (
        <PACalculationComponent
          deliverableCount={deliverableCount}
          className="col-span-2 h-[331px]"
          type={deliverableType}
          variant="card"
        />
      ) : (
        <CatalogItemDetailCardText
          info={t('common_pa_value')}
          onCopy={handleCopy}
          content={content}
          icon={icon}
          className="col-span-2 h-[331px]"
          type={deliverableType}
        />
      ),
    DELIVERABLES: (
      <CatalogItemDetailCardDeliverables
        info={t('common_deliverables')}
        onCopy={handleCopy}
        content={content}
        icon={icon}
        className="col-span-2 h-[331px] overflow-hidden"
      />
    ),
    DELIVERABLE: (
      <CatalogItemDetailCardText
        info={t('common_deliverable')}
        onCopy={handleCopy}
        content={content}
        icon={icon}
        className="col-span-2 h-[331px]"
      />
    ),
  };

  return cardMap[info] || <div>{t('common_no_info_available')}</div>;
}
