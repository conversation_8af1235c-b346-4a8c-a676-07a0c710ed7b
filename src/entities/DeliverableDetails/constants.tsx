export const DELIVERABLE_MAPPED_TYPES = {
  KPI: 'KPI',
  PROJECT: 'PROJECT',
  SCOPED_PROJECT_YES_NO: 'PROJECT',
};

export type PARule = {
  achieve: number;
  percentage: number;
  isOrFewer?: boolean;
  isAllOrNothing?: boolean;
};

export const PA_CALCULATION_RULES: Record<number, PARule[]> = {
  5: [
    { achieve: 5, percentage: 100 },
    { achieve: 4, percentage: 90 },
    { achieve: 3, percentage: 80 },
    { achieve: 2, percentage: 0, isOrFewer: true },
  ],
  4: [
    { achieve: 4, percentage: 100 },
    { achieve: 3, percentage: 80 },
    { achieve: 2, percentage: 0, isOrFewer: true },
  ],
  3: [
    { achieve: 3, percentage: 100, isAllOrNothing: true },
    { achieve: 0, percentage: 0, isAllOrNothing: true },
  ],
};
