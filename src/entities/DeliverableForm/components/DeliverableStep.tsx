import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Plus } from 'react-bootstrap-icons';
import {
  DndContext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { arrayMove } from '@dnd-kit/sortable';
import { useTranslate } from '@tolgee/react';
import { useFormikContext } from 'formik';

import { ActionModal } from '~/shared/components/ActionModal';
import { useActionModal } from '~/shared/components/ActionModal/useActionModal';
import {
  DraggableArea,
  DraggableSortableCard,
} from '~/shared/components/DragAndDrop';
import { MappedSelectedData } from '~/shared/components/DragAndDrop/DraggableArea';
import { Trash } from '~/shared/components/icons';
import {
  DeliverableItem,
  DeliverableTypeEnum,
} from '~/shared/types/Deliverable';

import { useDeliverables } from '../hooks';
import { useDeliverableFilter } from '../hooks/useDeliverableFilter';

import { DeliverableCard } from './DeliverableCard';
import { DeliverableDrawer } from './DeliverableDrawer';
import { DeliverableList } from './DeliverableList';

export function DeliverableStep({ type }: { type: DeliverableTypeEnum }) {
  const { t } = useTranslate();
  const { values, setFieldValue } = useFormikContext<any>();
  const actionModal = useActionModal();

  const DELIVERABLE_LIMITS = {
    [DeliverableTypeEnum.PROJECT]: 64,
  };

  const [selectedDeliverables, setSelectedDeliverables] = useState<
    DeliverableItem[]
  >([]);

  const [scopedDeliverables, setScopedDeliverables] = useState<
    DeliverableItem[]
  >([]);

  const scopedLoadedRef = useRef(false);

  const allDeliverables = useMemo(
    () => [...selectedDeliverables, ...scopedDeliverables],
    [selectedDeliverables, scopedDeliverables],
  );

  const [draggedDeliverable, setDraggedDeliverable] =
    useState<DeliverableItem | null>(null);
  const [isDraggingNewItem, setIsDraggingNewItem] = useState(false);
  const [isOpenDrawer, setIsOpenDrawer] = useState(false);
  const [mappedSelectedDeliverables, setMappedSelectedDeliverables] = useState<
    MappedSelectedData[]
  >([]);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 3,
      },
    }),
  );

  const {
    data: deliverablesData,
    isLoading: isDeliverablesLoading,
    isInitialLoading: isDeliverablesInitialLoading,
    isSearchLoading: isDeliverablesSearchLoading,
    isError: isDeliverablesError,
    filters,
    setFilters,
  } = useDeliverables();

  const { searchQuery, handleSearchChange, handleSearchKeyPress } =
    useDeliverableFilter(filters, setFilters);

  useEffect(() => {
    const deliverableUids = selectedDeliverables
      .filter(d => !d.uid.startsWith('temp-'))
      .map(deliverable => deliverable.uid);

    const scopedDeliverablesForForm = scopedDeliverables.map(deliverable => {
      const { uid, ...deliverableWithoutUid } = deliverable;
      return deliverableWithoutUid;
    });

    void setFieldValue('deliverableUids', deliverableUids);
    void setFieldValue(
      'deliverables',
      selectedDeliverables.filter(d => !d.uid.startsWith('temp-')),
    );
    void setFieldValue('scopedDeliverables', scopedDeliverablesForForm);
  }, [selectedDeliverables, scopedDeliverables, setFieldValue]);

  useEffect(() => {
    if (
      values.deliverables &&
      values.deliverables.length > 0 &&
      selectedDeliverables.filter(d => !d.uid.startsWith('temp-')).length === 0
    ) {
      setSelectedDeliverables(prev => [
        ...prev.filter(d => d.uid.startsWith('temp-')),
        ...values.deliverables,
      ]);
    }

    if (
      values.scopedDeliverables &&
      values.scopedDeliverables.length > 0 &&
      !scopedLoadedRef.current
    ) {
      const scopedWithTempUids = values.scopedDeliverables.map(
        (item: any, index: number) => ({
          ...item,
          uid: `temp-loaded-${index}-${Date.now()}`,
        }),
      );

      setScopedDeliverables(scopedWithTempUids);
      scopedLoadedRef.current = true;
    }
  }, [values.deliverables, values.scopedDeliverables, selectedDeliverables]);

  const isError = !!isDeliverablesLoading && isDeliverablesError;

  const availableDeliverables = useMemo(() => {
    const selectedUids = new Set(allDeliverables.map(d => d.uid));
    const currentEditingUid = (values as { uid?: string })?.uid;

    return (deliverablesData?.data || []).filter(
      d =>
        !selectedUids.has(d.uid) &&
        (!currentEditingUid || d.uid !== currentEditingUid),
    );
  }, [deliverablesData, allDeliverables, values]);

  const handleDragStart = (event: DragStartEvent) => {
    const deliverable = event.active.data.current?.data;
    if (deliverable) {
      setDraggedDeliverable(deliverable);
      const isFromSideList = availableDeliverables.some(
        d => d.uid === deliverable.uid,
      );
      setIsDraggingNewItem(isFromSideList);
    }
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (!over) {
      setDraggedDeliverable(null);
      setIsDraggingNewItem(false);
      return;
    }

    const activeDeliverable = active.data.current?.data;
    const isFromAvailableList =
      activeDeliverable &&
      !selectedDeliverables.find(d => d.uid === activeDeliverable.uid);

    const isReordering =
      allDeliverables.find(d => d.uid === over.id) &&
      allDeliverables.find(d => d.uid === active.id);

    if (isReordering) {
      const oldIndex = allDeliverables.findIndex(d => d.uid === active.id);
      const newIndex = allDeliverables.findIndex(d => d.uid === over.id);

      if (oldIndex !== newIndex) {
        const newAllDeliverables = arrayMove(
          allDeliverables,
          oldIndex,
          newIndex,
        );

        const newSelectedDeliverables = newAllDeliverables.filter(
          d => !d.uid.startsWith('temp-loaded-'),
        );
        const newScopedDeliverables = newAllDeliverables.filter(d =>
          d.uid.startsWith('temp-loaded-'),
        );

        setSelectedDeliverables(newSelectedDeliverables);
        setScopedDeliverables(newScopedDeliverables);
      }
    }

    const isAdding =
      over.id === 'selection-area' ||
      (isFromAvailableList && allDeliverables.find(d => d.uid === over.id));

    if (
      isAdding &&
      activeDeliverable &&
      !allDeliverables.find(d => d.uid === activeDeliverable.uid) &&
      !isAtLimit
    ) {
      setSelectedDeliverables(prev => [...prev, activeDeliverable]);
    }

    setDraggedDeliverable(null);
    setIsDraggingNewItem(false);
  };

  const handleDeliverableRemove = (deliverableId: string) => {
    setSelectedDeliverables(prev => prev.filter(d => d.uid !== deliverableId));

    if (deliverableId.startsWith('temp-')) {
      setScopedDeliverables(prev => prev.filter(d => d.uid !== deliverableId));
    }

    const updatedSelectedDeliverables = selectedDeliverables.filter(
      d => d.uid !== deliverableId,
    );
    const updatedScopedDeliverables = scopedDeliverables.filter(
      d => d.uid !== deliverableId,
    );

    const scopedDeliverablesForForm = updatedScopedDeliverables.map(
      deliverable => {
        const { uid, ...deliverableWithoutUid } = deliverable;
        return deliverableWithoutUid;
      },
    );

    void setFieldValue(
      'deliverables',
      updatedSelectedDeliverables.filter(d => !d.uid.startsWith('temp-')),
    );
    void setFieldValue('scopedDeliverables', scopedDeliverablesForForm);
  };

  const handleClearSelection = () => {
    setSelectedDeliverables([]);
    setScopedDeliverables([]);
    scopedLoadedRef.current = false;

    void setFieldValue('deliverableUids', []);
    void setFieldValue('deliverables', []);
    void setFieldValue('scopedDeliverables', []);
  };

  const handleAddNewDeliverable = (newDeliverable: DeliverableItem) => {
    // Adiciona nos scoped deliverables (items criados separadamente)
    setScopedDeliverables(prev => [...prev, newDeliverable]);
    scopedLoadedRef.current = true;
  };

  const handleClearDeliverable = () => {
    actionModal.openModal({
      title: t('common_clear_list'),
      message: t('common_do_you_really_want_to_clear_the_list'),
      actions: [
        {
          label: t('common_yes'),
          onClick: () => {},
        },
        {
          label: t('common_no'),
          onClick: actionModal.closeModal,
          variant: 'secondary',
        },
      ],
      onConfirm: () => {
        handleClearSelection();
        actionModal.closeModal();
      },
    });
  };

  useEffect(() => {
    setMappedSelectedDeliverables(
      allDeliverables.map(deliverable => ({
        uid: deliverable.uid,
      })),
    );
  }, [allDeliverables]);

  const maxItems =
    DELIVERABLE_LIMITS[type as keyof typeof DELIVERABLE_LIMITS] || Infinity;
  const isAtLimit = allDeliverables.length >= maxItems;
  const limitMessage = isAtLimit
    ? t('common_maximum_deliverables_reached', { max: maxItems })
    : undefined;

  return (
    <DndContext
      sensors={sensors}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <div className="flex gap-6 h-[calc(100vh-340px)]">
        <DeliverableList
          deliverables={availableDeliverables}
          title={t('common_deliverables')}
          subtitle={t('common_search_and_selected_deliverables')}
          showSearch
          searchTerm={searchQuery}
          placeholder={t('common_search_by_name')}
          onSearchChange={handleSearchChange}
          onSearchKeyPress={handleSearchKeyPress}
          isInitialLoading={isDeliverablesInitialLoading}
          isSearchLoading={isDeliverablesSearchLoading}
          isError={isError}
          renderItem={deliverable => (
            <DeliverableCard
              data={deliverable}
              isSelected={selectedDeliverables.some(
                d => d.uid === deliverable.uid,
              )}
              showActions={true}
              showDefinition={true}
            />
          )}
        />
        <DraggableArea
          type="DELIVERABLE"
          selectedData={mappedSelectedDeliverables}
          isDraggingNewItem={isDraggingNewItem}
          maxItems={maxItems}
          isAtLimit={isAtLimit}
          limitMessage={limitMessage}
          actions={[
            {
              label: t('common_create_deliverable'),
              onClick: () => setIsOpenDrawer(true),
              variant: 'secondary',
              iconLeft: <Plus />,
            },
            {
              label: t('common_clear_list'),
              onClick: handleClearDeliverable,
              variant: 'secondary',
              iconLeft: <Trash />,
              disabled: allDeliverables.length === 0,
            },
          ]}
        >
          {allDeliverables.map(deliverable => (
            <DraggableSortableCard key={deliverable.uid} id={deliverable.uid}>
              <DeliverableCard
                disableDrag
                data={deliverable}
                showActions={true}
                showDefinition={true}
                showRemoveAction={true}
                onRemoveActionClick={() =>
                  handleDeliverableRemove(deliverable.uid)
                }
              />
            </DraggableSortableCard>
          ))}
        </DraggableArea>
        <DragOverlay>
          {draggedDeliverable && (
            <DeliverableCard data={draggedDeliverable} isDragging />
          )}
        </DragOverlay>
        <DeliverableDrawer
          isOpen={isOpenDrawer}
          onClose={() => setIsOpenDrawer(false)}
          onAddDeliverable={handleAddNewDeliverable}
        />
        <ActionModal
          isOpen={actionModal.isOpen}
          openModal={actionModal.openModal}
          closeModal={actionModal.closeModal}
          title={actionModal.title}
          message={actionModal.message}
          actions={[
            {
              label: t('common_yes'),
              onClick: actionModal.handleConfirm,
              variant: 'primary',
            },
            {
              label: t('common_no'),
              onClick: actionModal.closeModal,
              variant: 'secondary',
            },
          ]}
        />
      </div>
    </DndContext>
  );
}
