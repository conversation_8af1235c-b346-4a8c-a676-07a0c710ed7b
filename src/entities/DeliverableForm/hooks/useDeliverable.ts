import { useRef, useState } from 'react';
import { useQuery } from '@tanstack/react-query';

import { DeliverableType } from '~/entities/Home/components/Filters/types';
import deliverablesService from '~/shared/services/deliverables';

export const useDeliverables = () => {
  const [filters, setFilters] = useState<{ fuzzy_search?: string }>({
    fuzzy_search: '',
  });

  const isInitialLoad = useRef(true);

  const { data, isLoading, isError, isFetching } = useQuery({
    queryKey: [
      'project-deliverables',
      DeliverableType.KPI,
      DeliverableType.PROJECT,
      filters,
    ],
    queryFn: () =>
      deliverablesService.getDeliverables({
        deliverableTypes: [DeliverableType.KPI, DeliverableType.PROJECT],
        pageNumber: 1,
        pageSize: 100,
        search: filters.fuzzy_search,
      }),
    onSuccess: () => {
      isInitialLoad.current = false;
    },
  });

  const isInitialLoading = isLoading && isInitialLoad.current;
  const isSearchLoading = isFetching && !isInitialLoad.current;

  return {
    data,
    isLoading,
    isInitialLoading,
    isSearchLoading,
    isError,
    filters,
    setFilters,
  };
};
