import { useEffect, useState } from 'react';

import { FilterOptions, SortOptions, OrderBy, SortBy } from '../components/Filters/types';

export function useFilters(
  externalFilters?: FilterOptions,
  setExternalFilters?: (filters: FilterOptions) => void,
  setPageNumber?: (page: number) => void,
) {
  const [searchQuery, setSearchQuery] = useState('');
  const [internalFilters, setInternalFilters] = useState<FilterOptions>({
    deliverableTypes: [],
    isActive: undefined,
    businessFunctions: [],
    fuzzy_search: '',
    orderBy: null,
    sortBy: null,
  });

  const filters = externalFilters || internalFilters;
  const setFilters = setExternalFilters || setInternalFilters;

  useEffect(() => {
    if (externalFilters) {
      setInternalFilters(externalFilters);
    }
  }, [externalFilters]);

  const [deliverableTypes, setDeliverableTypes] = useState<string[]>(
    Array.isArray(filters?.deliverableTypes)
      ? filters.deliverableTypes
      : filters?.deliverableTypes
      ? [filters.deliverableTypes]
      : [],
  );
  const [showInactive, setShowInactive] = useState<boolean>(
    filters?.isActive === false,
  );
  const [selectedFunctions, setSelectedFunctions] = useState<string[]>(
    filters?.stagedBusinessFunctions ?? filters?.businessFunctions ?? [],
  );

  useEffect(() => {
    setSelectedFunctions(
      filters?.stagedBusinessFunctions ?? filters?.businessFunctions ?? [],
    );
  }, [filters?.stagedBusinessFunctions, filters?.businessFunctions]);

  useEffect(() => {
    setFilters({
      ...filters,
      deliverableTypes,
      isActive: showInactive ? false : undefined,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [deliverableTypes, showInactive]);

  const handleLevelChange = (values: string[]) => {
    const levelValues = values.filter(v => v !== 'INACTIVE');

    if (levelValues.length === 0) {
      setDeliverableTypes([]);
    } else {
      const newLevel = levelValues[levelValues.length - 1] ?? '';
      setDeliverableTypes([newLevel]);
    }

    setShowInactive(values.includes('INACTIVE'));
  };

  const handleFunctionToggle = (functionName: string) => {
    setSelectedFunctions(prev => {
      const next = prev.includes(functionName)
        ? prev.filter(f => f !== functionName)
        : [...prev, functionName];
      setFilters({
        ...filters,
        stagedBusinessFunctions: next,
      });
      return next;
    });
  };

  const handleSearchChange = (query: string) => {
    setSearchQuery(query);

    if (!query.trim()) {
      setFilters({
        ...filters,
        fuzzy_search: '',
      });
    }
  };

  const handleSearchSubmit = () => {
    const nextBusinessFunctions =
      filters?.stagedBusinessFunctions ?? selectedFunctions ?? [];

    if (setPageNumber) {
      setPageNumber(1);
    }

    setFilters({
      ...filters,
      fuzzy_search: searchQuery,
      businessFunctions: nextBusinessFunctions,
      stagedBusinessFunctions: nextBusinessFunctions,
      forceSearch: !filters.forceSearch,
    });
  };

  const handleSearchKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearchSubmit();
    }
  };

  const handleSortOptionsChange = (sortOptions: SortOptions | null) => {
    setFilters({
      ...filters,
      orderBy: sortOptions?.orderBy || null,
      sortBy: sortOptions?.sortBy || null,
    });
  };

  const toggleValues = [
    ...deliverableTypes,
    ...(showInactive ? ['INACTIVE'] : []),
  ].filter(Boolean);

  return {
    filters,
    setFilters,
    searchQuery,
    setSearchQuery,
    handleSearchChange,
    handleSearchKeyPress,
    handleSearchSubmit,
    orderBy: filters.orderBy,
    sortBy: filters.sortBy,
    handleSortOptionsChange,
    showInactive,
    setShowInactive,
    selectedFunctions,
    setSelectedFunctions,
    handleLevelChange,
    handleFunctionToggle,
    toggleValues,
  };
}
