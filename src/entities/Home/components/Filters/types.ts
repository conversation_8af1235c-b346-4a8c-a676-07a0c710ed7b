export type FiltersProps = {
  setFilters: (filters: FilterOptions) => void;
  filters?: FilterOptions;
};

export enum DeliverableType {
  KPI = 'KPI',
  SCOPED_PROJECT_YES_NO = 'SCOPED_PROJECT_YES_NO',
  PROJECT = 'PROJECT',
}

export enum OrderBy {
  ASC = 'asc',
  DESC = 'desc',
}

export enum SortBy {
  NAME = 'name',
  USAGE = 'usage',
}

export interface SortOptions {
  orderBy: OrderBy;
  sortBy: SortBy;
}

export interface FilterOptions {
  deliverableTypes?: string[];
  isActive?: boolean;
  businessFunctions: string[];
  stagedBusinessFunctions?: string[];
  fuzzy_search?: string;
  orderBy?: OrderBy | null | undefined;
  sortBy?: SortBy | null | undefined;
  forceSearch?: boolean;
}
