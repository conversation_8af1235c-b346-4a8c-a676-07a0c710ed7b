import React from 'react';
import { Filter } from 'react-bootstrap-icons';
import {
  Button,
  Container,
  DropdownMenu,
  Typography,
} from '@ghq-abi/design-system-v2';
import { useTranslate } from '@tolgee/react';

import { OrderBy, SortBy, SortOptions } from '../Filters/types';

type SortDropdownProps = {
  orderBy?: OrderBy | null | undefined;
  sortBy?: SortBy | null | undefined;
  onSortOptionsChange: (options: SortOptions | null) => void;
};

export function SortDropdown({
  orderBy,
  sortBy,
  onSortOptionsChange
}: SortDropdownProps) {
  const { t } = useTranslate();

  const getCurrentValue = () => {
    if (orderBy && sortBy) {
      return `${sortBy}-${orderBy}`;
    }
    return '';
  };

  const handleValueChange = (value: string) => {
    if (value.includes('-')) {
      const [newSortBy, newOrderBy] = value.split('-');
      onSortOptionsChange({
        sortBy: newSortBy as SortBy,
        orderBy: newOrderBy as OrderBy,
      });
    } else {
      onSortOptionsChange(null);
    }
  };

  return (
    <DropdownMenu.Root>
      <DropdownMenu.Trigger asChild>
        <Button variant="tertiary" border="default" iconLeft={<Filter />}>
          <Typography variant="body-sm-regular">
            {t('common_sort_by')}
          </Typography>
        </Button>
      </DropdownMenu.Trigger>
      <DropdownMenu.Content className="w-48">
        <DropdownMenu.RadioGroup
          className="flex flex-col gap-2"
          value={getCurrentValue()}
          onValueChange={handleValueChange}
        >
          <DropdownMenu.RadioItem value={`${SortBy.NAME}-${OrderBy.ASC}`} className="w-full">
            <Typography variant="body-sm-regular">{t('common_a_z')}</Typography>
          </DropdownMenu.RadioItem>
          <DropdownMenu.RadioItem value={`${SortBy.NAME}-${OrderBy.DESC}`} className="w-full">
            <Typography variant="body-sm-regular">{t('common_z_a')}</Typography>
          </DropdownMenu.RadioItem>
          <DropdownMenu.RadioItem value={`${SortBy.USAGE}-${OrderBy.DESC}`} className="w-full">
            <Typography variant="body-sm-regular">{t('common_usage_most_used')}</Typography>
          </DropdownMenu.RadioItem>
          <DropdownMenu.RadioItem value={`${SortBy.USAGE}-${OrderBy.ASC}`} className="w-full">
            <Typography variant="body-sm-regular">{t('common_usage_least_used')}</Typography>
          </DropdownMenu.RadioItem>
        </DropdownMenu.RadioGroup>
      </DropdownMenu.Content>
    </DropdownMenu.Root>
  );
}
