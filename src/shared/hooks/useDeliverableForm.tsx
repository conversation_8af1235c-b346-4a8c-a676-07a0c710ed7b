import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { useMutation } from '@tanstack/react-query';
import { useTranslate } from '@tolgee/react';
import { useFormik } from 'formik';
import * as Yup from 'yup';

import { DeliverableStep } from '~/entities/DeliverableForm/components';
import { OwnersStep } from '~/entities/DeliverableForm/components/OwnersStep';
import { useActionModal } from '~/shared/components/ActionModal/useActionModal';
import { DeliverableFormCard } from '~/shared/components/DeliverableFormCard';
import deliverablesService from '~/shared/services/deliverables';
import {
  CreateDeliverableItem,
  DeliverableTypeEnum,
} from '~/shared/types/Deliverable';

interface UseDeliverableFormProps {
  initialKpi?: any;
  isEdit?: boolean;
}

export function useDeliverableForm({
  initialKpi,
  isEdit = false,
}: UseDeliverableFormProps) {
  const { t } = useTranslate();
  const router = useRouter();
  const type = (
    initialKpi ? initialKpi.type : router.query?.type || DeliverableTypeEnum.KPI
  ) as DeliverableTypeEnum;
  const createDeliverableItemModal = useActionModal();
  const createDeliverableItemConfirmationModal = useActionModal();
  const [step, setStep] = useState(0);
  const [formValues, setFormValues] = useState<any>(null);

  const defaultSteps = type === DeliverableTypeEnum.KPI
    ? [
        {
          id: 1,
          title: t('common_information'),
          status: (step > 0
            ? 'completed'
            : step === 0
            ? 'current'
            : 'pending') as 'completed' | 'current' | 'pending',
          content: <DeliverableFormCard />,
        },
        {
          id: 2,
          title: t('common_deliverable_owner'),
          status: (step === 1
            ? 'current'
            : step > 1
            ? 'completed'
            : 'pending') as 'completed' | 'current' | 'pending',
          content: <OwnersStep />,
        },
      ]
    : [
        {
          id: 1,
          title: t('common_information'),
          status: (step > 0
            ? 'completed'
            : step === 0
            ? 'current'
            : 'pending') as 'completed' | 'current' | 'pending',
          content: <DeliverableFormCard />,
        },
        {
          id: 2,
          title: t('common_deliverables'),
          status: (step === 1
            ? 'current'
            : step > 1
            ? 'completed'
            : 'pending') as 'completed' | 'current' | 'pending',
          content: <DeliverableStep type={type} />,
        },
        {
          id: 3,
          title: t('common_deliverable_owner'),
          status: (step === 2
            ? 'current'
            : step > 2
            ? 'completed'
            : 'pending') as 'completed' | 'current' | 'pending',
          content: <OwnersStep />,
        },
      ];

  const handleNext = () =>
    setStep(s => Math.min(s + 1, defaultSteps.length - 1));
  const handleBack = () => setStep(s => Math.max(s - 1, 0));

  const createInitialValues = (): CreateDeliverableItem => {
    if (isEdit && initialKpi) {
      return initialKpi;
    }

    return {
      name: '',
      businessFunction: '',
      frequency: '',
      definition: '',
      buLevelAggregation: '',
      calculationMethod: '',
      paValue: '',
      dataSource: '',
      isActive: false,
      type: type || DeliverableTypeEnum.KPI,
      dateStart: null,
      dateEnd: null,
      deliverableUids: [],
      deliverables: [],
      owners: [],
      scopedDeliverables: [],
    };
  };

  const initialValues = createInitialValues();

  const validationSchema = Yup.object({
    name: Yup.string().required(
      `${t('common_name')} ${t('common_is_required')}`,
    ),
    businessFunction: Yup.string().required(
      `${t('common_function')} ${t('common_is_required')}`,
    ),
    frequency: Yup.string().required(
      `${t('common_frequency')} ${t('common_is_required')}`,
    ),
    definition: Yup.string().required(
      `${t('common_definition')} ${t('common_is_required')}`,
    ),
    buLevelAggregation: Yup.string(),
    calculationMethod: Yup.string().when({
      is: () =>
        [DeliverableTypeEnum.KPI].includes(
          type,
        ),
      then: schema =>
        schema.required(
          `${t('common_calculation_method')} ${t('common_is_required')}`,
        ),
      otherwise: schema => schema.notRequired(),
    }),
    dataSource: Yup.string().required(
      `${t('common_data_source')} ${t('common_is_required')}`,
    ),
    paValue: Yup.string(),
    dateStart: Yup.string().when({
      is: () =>
        [
          DeliverableTypeEnum.PROJECT,
        ].includes(type),
      then: schema =>
        schema.required(`${t('common_start_date')} ${t('common_is_required')}`),
      otherwise: schema => schema.notRequired(),
    }),
    dateEnd: Yup.string().when({
      is: () =>
        [
          DeliverableTypeEnum.PROJECT,
        ].includes(type),
      then: schema =>
        schema.required(`${t('common_end_date')} ${t('common_is_required')}`),
      otherwise: schema => schema.notRequired(),
    }),
  });

  const { isLoading, mutate } = useMutation({
    mutationFn: (values: CreateDeliverableItem) => {
      if (isEdit && initialKpi?.uid) {
        return deliverablesService.updateDeliverable(initialKpi.uid, {
          ...values,
        });
      } else {
        return deliverablesService.createDeliverable({
          ...values,
        });
      }
    },
    onSuccess: response => {
      createDeliverableItemConfirmationModal.closeModal();
      setFormValues(null);

      if (isEdit) {
        createDeliverableItemModal.openModal({
          title: t('common_congratulations'),
          message: t('common_kpi_updated_successfully'),
          variant: 'success',
          actions: [
            {
              label: t('common_navigate_back_to_catalog'),
              onClick: handleNavigateBackToCatalog,
              variant: 'primary',
            },
            {
              label: t('common_keep_editing'),
              onClick: createDeliverableItemModal.closeModal,
              variant: 'secondary',
              border: 'default',
            },
          ],
        });
      } else {
        createDeliverableItemModal.openModal({
          title: t('common_congratulations'),
          message: t('common_kpi_created_successfully'),
          variant: 'success',
          actions: [
            {
              label: t('common_navigate_back_to_catalog'),
              onClick: handleNavigateBackToCatalog,
              variant: 'primary',
            },
          ],
        });
      }
    },
    onError: error => {
      createDeliverableItemConfirmationModal.closeModal();
      setFormValues(null);

      createDeliverableItemModal.openModal({
        title: t('common_warning'),
        message: isEdit
          ? t('common_error_updating_kpi')
          : t('common_error_creating_kpi'),
        variant: 'warning',
      });
    },
  });

  const handleSubmit = (values: typeof initialValues) => {
    try {
      setFormValues(values);
      createDeliverableItemConfirmationModal.openModal({
        title: isEdit ? t('common_update_item') : t('common_create_item'),
        message: isEdit
          ? t('common_confirm_update_item')
          : t('common_confirm_create_item'),
        variant: 'warning',
        actions: [
          {
            label: isEdit ? t('common_update') : t('common_create'),
            onClick: () => mutate(values),
            variant: 'primary',
            isLoading: isLoading,
          },
          {
            label: t('common_keep_editing'),
            onClick: createDeliverableItemConfirmationModal.closeModal,
            variant: 'secondary',
            border: 'default',
          },
        ],
      });
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  useEffect(() => {
    if (createDeliverableItemConfirmationModal.isOpen && formValues) {
      createDeliverableItemConfirmationModal.openModal({
        title: isEdit ? t('common_update_item') : t('common_create_item'),
        message: isEdit
          ? t('common_confirm_update_item')
          : t('common_confirm_create_item'),
        variant: 'warning',
        actions: [
          {
            label: isEdit ? t('common_update') : t('common_create'),
            onClick: isLoading ? () => {} : () => mutate(formValues),
            variant: 'primary',
            isLoading: isLoading,
          },
          {
            label: t('common_keep_editing'),
            onClick: isLoading
              ? () => {}
              : createDeliverableItemConfirmationModal.closeModal,
            variant: 'secondary',
            border: 'default',
          },
        ],
      });
    }
  }, [
    isLoading,
    createDeliverableItemConfirmationModal.isOpen,
    isEdit,
    t,
    createDeliverableItemConfirmationModal,
    formValues,
    mutate,
  ]);

  const formik = useFormik({
    initialValues,
    validationSchema,
    enableReinitialize: isEdit,
    onSubmit: handleSubmit,
  });

  const handleNavigateBackToCatalog = async () => {
    await router.push('/');
  };

  const handleNavigateToEditKpi = async (uid: string) => {
    await router.push(`/deliverables/edit/${uid}`);
  };

  return {
    formik,
    isLoading,
    createDeliverableItemModal,
    createDeliverableItemConfirmationModal,
    handleNavigateBackToCatalog,
    handleBack,
    handleNext,
    step,
    setStep,
    defaultSteps,
    isEdit,
    title:
      type === DeliverableTypeEnum.KPI
        ? t('common_create_item')
        : t('common_create_project'),
  };
}
