import React from 'react';
import { Card, Container } from '@ghq-abi/design-system-v2';
import { useTranslate } from '@tolgee/react';
import { useFormikContext } from 'formik';

import { DeliverableTypeEnum } from '~/shared/types/Deliverable';

import { FormikDate } from '../FormikDate';
import { FormikInput } from '../FormikInput';
import { FormikSelect } from '../FormikSelect';
import { FormikTextarea } from '../FormikTextarea';

import { useDeliverableFormCard } from './useDeliverableFormCard';

export function DeliverableFormCard() {
  const { t } = useTranslate();
  const { functionOptions, frequencyOptions } = useDeliverableFormCard();
  const { values, handleChange } = useFormikContext<any>();

  const type = values?.type as DeliverableTypeEnum;

  return (
    <>
      <Card.Root className="h-full" round="md">
        <Card.Content className="flex flex-col gap-4 p-6">
          <Container className="grid grid-cols-2 w-full items-center gap-4">
            <Container className="col-span-1">
              <FormikInput
                name="name"
                label={
                  <>
                    {t('common_name')}
                    <span className="text-red-500 ml-1">*</span>
                  </>
                }
                placeholder={t('common_insert_here')}
                onChange={handleChange}
                value={values.name}
              />
            </Container>
            <Container className="col-span-1">
              <FormikInput
                name="dataSource"
                label={
                  <>
                    {t('common_data_source')}
                    <span className="text-red-500 ml-1">*</span>
                  </>
                }
                placeholder={t('common_insert_here')}
                onChange={handleChange}
                value={values.dataSource}
              />
            </Container>
          </Container>

          <Container className="grid grid-cols-2 w-full items-center gap-4">
            <FormikSelect
              name="businessFunction"
              label={
                <>
                  {t('common_function')}
                  <span className="text-red-500 ml-1">*</span>
                </>
              }
              options={functionOptions}
              onChange={handleChange}
              value={values.businessFunction}
              placeholder={t('common_insert_here')}
            />
            <FormikSelect
              name="frequency"
              label={
                <>
                  {t('common_frequency')}
                  <span className="text-red-500 ml-1">*</span>
                </>
              }
              options={frequencyOptions}
              onChange={handleChange}
              value={values.frequency}
              placeholder={t('common_insert_here')}
            />
          </Container>

          {type === DeliverableTypeEnum.PROJECT && (
            <Container className="grid grid-cols-2 w-full items-center gap-4">
              <FormikDate
                name="dateStart"
                label={
                  <>
                    {t('common_start_date')}
                    <span className="text-red-500 ml-1">*</span>
                  </>
                }
                onChange={handleChange}
                value={values.dateStart}
                placeholder={t('common_insert_here')}
              />
              <FormikDate
                name="dateEnd"
                label={
                  <>
                    {t('common_end_date')}
                    <span className="text-red-500 ml-1">*</span>
                  </>
                }
                onChange={handleChange}
                value={values.dateEnd}
                placeholder={t('common_insert_here')}
              />
            </Container>
          )}

          <Container className="grid grid-cols-2 w-full items-center gap-4">
            <FormikTextarea
              name="definition"
              label={
                <>
                  {t('common_definition')}
                  <span className="text-red-500 ml-1">*</span>
                </>
              }
              placeholder={t('common_insert_here')}
              onChange={handleChange}
              value={values.definition}
            />
            <FormikTextarea
              name="buLevelAggregation"
              label={`${t('common_bu_level_aggregation')} (${t(
                'common_optional',
              )})`}
              placeholder={t('common_insert_here')}
              onChange={handleChange}
              value={values.buLevelAggregation}
            />
          </Container>

          <Container className="grid grid-cols-2 w-full items-center gap-4">
            {type === DeliverableTypeEnum.KPI && (
              <FormikTextarea
                name="calculationMethod"
                label={
                  <>
                    {t('common_calculation_method')}
                    <span className="text-red-500 ml-1">*</span>
                  </>
                }
                placeholder={t('common_insert_here')}
                onChange={handleChange}
                value={values.calculationMethod}
              />
            )}
            <FormikTextarea
              name="paValue"
              label={`${t('common_pa_value')} (${t('common_optional')})`}
              placeholder={t('common_insert_here')}
              onChange={handleChange}
              value={values.paValue}
            />
          </Container>
        </Card.Content>
      </Card.Root>
    </>
  );
}
