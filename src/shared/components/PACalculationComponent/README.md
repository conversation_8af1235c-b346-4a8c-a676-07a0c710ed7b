# PA Calculation Component

A reusable component that displays PA (Performance Achievement) calculation rules for PROJECT type deliverables, replacing the standard paValue field with custom business rules.

## Features

- **Visual Design**: Matches the deliverables list design pattern from the application
- **Dynamic Rules**: Displays different PA calculation rules based on deliverable count
- **Two Variants**: 
  - `card`: For DeliverableDetails page (full card layout)
  - `simple`: For forms and drawers (compact layout)
- **Internationalization**: Uses Tolgee translation keys
- **Consistent Styling**: Follows existing design system patterns

## Business Rules

### For 5 deliverables:
- Achieve 5 → 100% achievement
- Achieve 4 → 90% achievement
- Achieve 3 → 80% achievement
- Achieve 2 or fewer → 0% achievement

### For 4 deliverables:
- Achieve 4 → 100% achievement
- Achieve 3 → 80% achievement
- Achieve 2 or fewer → 0% achievement

### For 3 or fewer deliverables:
- All-or-nothing: 100% (complete all) or 0% (any incomplete)

## Usage

```tsx
import { PACalculationComponent } from '~/shared/components/PACalculationComponent';

// Card variant (for DeliverableDetails page)
<PACalculationComponent
  deliverableCount={5}
  type={DeliverableTypeEnum.PROJECT}
  variant="card"
  className="col-span-2 h-[331px]"
/>

// Simple variant (for forms and drawers)
<PACalculationComponent
  deliverableCount={4}
  type={DeliverableTypeEnum.PROJECT}
  variant="simple"
/>
```

## Integration Points

The component is integrated in three locations:

1. **DeliverableDetails Page**: Replaces PA VALUE card for PROJECT types
2. **DeliverableFormCard**: Replaces paValue FormikTextarea for PROJECT types  
3. **CatalogItemDetailDrawer**: Replaces PA value InfoRow for PROJECT types

## Translation Keys

- `common_pa_value` - "PA VALUE"
- `common_pa_achieve_5` - "Achieve 5"
- `common_pa_achieve_4` - "Achieve 4"
- `common_pa_achieve_3` - "Achieve 3"
- `common_pa_achieve_2` - "Achieve 2 or fewer"
- `common_pa_achievement_100` - "100% achievement"
- `common_pa_achievement_90` - "90% achievement"
- `common_pa_achievement_80` - "80% achievement"
- `common_pa_achievement_0` - "0% achievement"
- `common_pa_3_deliverables` - For 3 or fewer deliverables scenario
