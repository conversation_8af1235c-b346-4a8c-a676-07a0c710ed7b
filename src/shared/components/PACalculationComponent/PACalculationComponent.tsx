import React from 'react';
import { Trophy } from 'react-bootstrap-icons';
import {
  Card,
  Container,
  IconWrapper,
  Typography,
} from '@ghq-abi/design-system-v2';
import { useTranslate } from '@tolgee/react';

import { DeliverableTypeEnum } from '~/shared/types/Deliverable';
import { cn } from '~/shared/utils/cn';
import { PA_CALCULATION_RULES, PARule } from '~/entities/DeliverableDetails/constants';
import { PACalculationComponentProps } from './types';


export function PACalculationComponent({
  deliverableCount,
  className,
  type,
  variant = 'card',
}: PACalculationComponentProps) {
  const { t } = useTranslate();
  
  // Determine which rules to use based on deliverable count
  const getRules = () => {
    if (deliverableCount >= 5) {
      return PA_CALCULATION_RULES[5];
    } else if (deliverableCount === 4) {
      return PA_CALCULATION_RULES[4];
    } else {
      return PA_CALCULATION_RULES[3];
    }
  };

  const rules = getRules();
  const iconVariant = type !== DeliverableTypeEnum.KPI ? 'primary' : 'secondary';

  // Helper function to get translation key for achievement text
  const getAchievementText = (rule: PARule) => {
    if (rule.isAllOrNothing && rule.achieve === 3) {
      return t('common_pa_3_deliverables');
    }
    if (rule.isAllOrNothing && rule.achieve === 0) {
      return t('common_pa_3_deliverables'); // Same text for all-or-nothing scenario
    }
    if (rule.isOrFewer) {
      return t('common_pa_achieve_2');
    }

    switch (rule.achieve) {
      case 5:
        return t('common_pa_achieve_5');
      case 4:
        return t('common_pa_achieve_4');
      case 3:
        return t('common_pa_achieve_3');
      default:
        return `${rule.achieve}`;
    }
  };

  // Helper function to get translation key for percentage text
  const getPercentageText = (percentage: number) => {
    switch (percentage) {
      case 100:
        return t('common_pa_achievement_100');
      case 90:
        return t('common_pa_achievement_90');
      case 80:
        return t('common_pa_achievement_80');
      case 0:
        return t('common_pa_achievement_0');
      default:
        return `${percentage}%`;
    }
  };

  if (variant === 'simple') {
    return (
      <Container className={cn('flex flex-col gap-2', className)}>
        <Container className="flex items-center gap-2">
          <Trophy size={20} />
          <Typography variant="body-sm-bold">
            {t('common_pa_value')}
          </Typography>
        </Container>
        <Container className="flex flex-col gap-2">
          {rules.map((rule, index) => (
            <Container
              key={index}
              background="transparent"
              border="default"
              round="minimal"
              className="flex gap-2 items-center justify-between p-3 shadow-sm"
            >
              <Typography variant="body-sm-regular">
                {getAchievementText(rule)}
              </Typography>
              <Typography variant="body-sm-regular" color="light">
                {getPercentageText(rule.percentage)}
              </Typography>
            </Container>
          ))}
        </Container>
      </Container>
    );
  }

  return (
    <Card.Root className={cn('p-4 flex flex-col', className)}>
      <Card.Header className="p-0 max-h-8 flex-shrink-0">
        <Card.Title className="flex items-center gap-4">
          <IconWrapper variant={iconVariant} round="md" size={32}>
            <Trophy />
          </IconWrapper>
          <div className="flex flex-col">
            <Typography variant="metadata-sm-bold" className="uppercase">
              {t('common_pa_value')}
            </Typography>
          </div>
        </Card.Title>
      </Card.Header>
      <Card.Content className="flex flex-col px-0 gap-4 h-[calc(100%-30px)] overflow-y-auto">
        {rules.map((rule, index) => (
          <Container
            key={index}
            background="transparent"
            border="default"
            round="minimal"
            className="flex gap-2 items-center justify-between p-4 h-16 shadow-sm"
          >
            <Typography variant="body-sm-bold">
              {getAchievementText(rule)}
            </Typography>
            <Typography variant="body-sm-regular" color="light">
              {getPercentageText(rule.percentage)}
            </Typography>
          </Container>
        ))}
      </Card.Content>
    </Card.Root>
  );
}
