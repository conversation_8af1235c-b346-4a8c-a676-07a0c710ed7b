import { GetStaticProps } from 'next';

import * as Home from '~/entities/Home';
import { DeliverableType } from '~/entities/Home/components/Filters/types';
import { PAGINATION_CONFIG } from '~/shared/constants/pagination';
import deliverablesService from '~/shared/services/deliverables';
import { DeliverableItemsResponse } from '~/shared/types/DeliverablesService';

interface IndexProps {
  initialKpis: DeliverableItemsResponse;
}

export default function Index({ initialKpis }: IndexProps) {
  return <Home.Page initialKpis={initialKpis} />;
}

export const getStaticProps: GetStaticProps<IndexProps> = async () => {
  try {
    const initialKpis = await deliverablesService.getDeliverables({
      pageNumber: PAGINATION_CONFIG.DEFAULT_PAGE_NUMBER,
      pageSize: PAGINATION_CONFIG.DELIVERABLES_PAGE_SIZE,
      deliverableTypes: [
        DeliverableType.KPI,
        DeliverableType.PROJECT,
      ],
    });

    return {
      props: {
        initialKpis,
      },
    };
  } catch (error) {
    console.error('Error fetching initial data:', error);

    return {
      props: {
        initialKpis: {
          data: [],
          pageNumber: PAGINATION_CONFIG.DEFAULT_PAGE_NUMBER,
          pageSize: PAGINATION_CONFIG.DELIVERABLES_PAGE_SIZE,
          totalRecords: 0,
        },
      },
    };
  }
};
